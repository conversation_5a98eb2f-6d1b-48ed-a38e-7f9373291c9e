// Mobile device detection utility
export const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  // Check for mobile user agents
  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
  const isMobileUA = mobileRegex.test(userAgent.toLowerCase());
  
  // Check for touch capability
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  // Check screen size
  const isSmallScreen = window.innerWidth <= 768;
  
  // Check for mobile-specific features
  const hasOrientationAPI = typeof window.orientation !== 'undefined';
  
  return isMobileUA || (isTouchDevice && isSmallScreen) || hasOrientationAPI;
};

// Check if device has limited performance capabilities
export const isLowPerformanceDevice = () => {
  if (typeof window === 'undefined') return false;
  
  // Check for low-end mobile devices
  const lowEndDevices = /android.*mobile|webos|iphone|ipod|blackberry|iemobile/i;
  const userAgent = navigator.userAgent || '';
  
  // Check hardware concurrency (CPU cores)
  const cpuCores = navigator.hardwareConcurrency || 1;
  
  // Check device memory (if available)
  const deviceMemory = navigator.deviceMemory || 1;
  
  return (
    lowEndDevices.test(userAgent.toLowerCase()) ||
    cpuCores <= 2 ||
    deviceMemory <= 2 ||
    isMobileDevice()
  );
};

// Get appropriate performance settings based on device
export const getPerformanceSettings = () => {
  const isMobile = isMobileDevice();
  const isLowPerf = isLowPerformanceDevice();
  
  return {
    isMobile,
    isLowPerformance: isLowPerf,
    particleCount: isLowPerf ? 1000 : isMobile ? 3000 : 8000,
    enableComplexAnimations: !isLowPerf,
    enableShadows: !isMobile,
    pixelRatio: isMobile ? Math.min(window.devicePixelRatio, 2) : [1, 2],
    antialias: !isMobile,
  };
};
