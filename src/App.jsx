import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { useState } from "react";

import { About, Contact, TechQuotes, Hero, Navbar, Tech, Works, StarsCanvas } from "./components";
import WelcomeScreen from "./components/WelcomeScreen";
import MobileTestComponent from "./components/MobileTestComponent";

const App = () => {
  const [showWelcome, setShowWelcome] = useState(true);

  const handleLoadingComplete = () => {
    setShowWelcome(false);
  };

  return (
    <>
      {showWelcome && <WelcomeScreen onLoadingComplete={handleLoadingComplete} />}

      {!showWelcome && (
        <BrowserRouter>
          <div className='relative z-0 bg-primary'>
            {/* Global starry background for all sections */}
            <StarsCanvas />

            {/* Navigation bar with highest z-index */}
            <Navbar />

            {/* Mobile test component - remove in production */}
            <MobileTestComponent />

            <div className='relative z-10'>
              <Hero />
            </div>
            <div className='relative z-10'>
              <About />
            </div>
            <div className='relative z-10'>
              <Tech />
            </div>
            <div className='relative z-10'>
              <Works />
            </div>
            <div className='relative z-10'>
              <TechQuotes />
            </div>
            <div className='relative z-10'>
              <Contact />
            </div>
          </div>
        </BrowserRouter>
      )}
    </>
  );
}

export default App;
