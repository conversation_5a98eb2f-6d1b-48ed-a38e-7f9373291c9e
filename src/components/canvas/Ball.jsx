import { Suspense, useState, useEffect } from "react";
import { Canvas } from "@react-three/fiber";
import {
  Decal,
  Float,
  OrbitControls,
  Preload,
  useTexture,
} from "@react-three/drei";

import CanvasLoader from "../Loader";
import { getPerformanceSettings } from "../../utils/deviceDetection";

const Ball = (props) => {
  const [decal] = useTexture([props.imgUrl]);

  return (
    <Float speed={1.75} rotationIntensity={1} floatIntensity={2}>
      <ambientLight intensity={0.25} />
      <directionalLight position={[0, 0, 0.05]} />
      <mesh castShadow receiveShadow scale={2.75}>
        <icosahedronGeometry args={[1, 1]} />
        <meshStandardMaterial
          color='#fff8eb'
          polygonOffset
          polygonOffsetFactor={-5}
          flatShading
        />
        <Decal
          position={[0, 0, 1]}
          rotation={[2 * Math.PI, 0, 6.25]}
          scale={1}
          map={decal}
          flatShading
        />
      </mesh>
    </Float>
  );
};

const BallCanvas = ({ icon }) => {
  const [performanceSettings, setPerformanceSettings] = useState(null);

  useEffect(() => {
    setPerformanceSettings(getPerformanceSettings());
  }, []);

  // Show loading until performance settings are determined
  if (!performanceSettings) {
    return <CanvasLoader />;
  }

  // For very low performance devices, show a simple fallback
  if (performanceSettings.isLowPerformance && performanceSettings.isMobile) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800/20 to-gray-600/20 rounded-lg">
        <img
          src={icon}
          alt="Technology"
          className="w-12 h-12 object-contain opacity-80"
        />
      </div>
    );
  }

  return (
    <Canvas
      frameloop='demand'
      dpr={performanceSettings.pixelRatio}
      gl={{
        preserveDrawingBuffer: true,
        antialias: performanceSettings.antialias,
        powerPreference: "high-performance"
      }}
    >
      <Suspense fallback={<CanvasLoader />}>
        <OrbitControls enableZoom={false} />
        <Ball imgUrl={icon} />
      </Suspense>

      <Preload all />
    </Canvas>
  );
};

export default BallCanvas;
