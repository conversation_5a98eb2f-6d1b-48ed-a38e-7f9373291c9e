import React, { Suspense, useEffect, useState } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Preload, useGLTF } from "@react-three/drei";

import CanvasLoader from "../Loader";
import { getPerformanceSettings } from "../../utils/deviceDetection";

const Computers = ({ isMobile, performanceSettings }) => {
  const computer = useGLTF("./desktop_pc/scene.gltf");

  // Validate the loaded model
  if (!computer.scene) {
    console.warn('Computer model failed to load');
    return null;
  }

  return (
    <mesh>
      <hemisphereLight intensity={0.15} groundColor='black' />
      <spotLight
        position={[-20, 50, 10]}
        angle={0.12}
        penumbra={1}
        intensity={1}
        castShadow={performanceSettings.enableShadows}
        shadow-mapSize={performanceSettings.enableShadows ? 1024 : 512}
      />
      <pointLight intensity={1} />
      <primitive
        object={computer.scene}
        scale={isMobile ? 0.7 : 0.75}
        position={isMobile ? [0, -3, -2.2] : [0, -3.25, -1.5]}
        rotation={[-0.01, -0.2, -0.1]}
      />
    </mesh>
  );
};

const ComputersCanvas = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [performanceSettings, setPerformanceSettings] = useState(null);

  useEffect(() => {
    // Get performance settings
    const settings = getPerformanceSettings();
    setPerformanceSettings(settings);

    // Add a listener for changes to the screen size
    const mediaQuery = window.matchMedia("(max-width: 500px)");

    // Set the initial value of the `isMobile` state variable
    setIsMobile(mediaQuery.matches || settings.isMobile);

    // Define a callback function to handle changes to the media query
    const handleMediaQueryChange = (event) => {
      setIsMobile(event.matches);
    };

    // Add the callback function as a listener for changes to the media query
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    // Remove the listener when the component is unmounted
    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
    };
  }, []);

  // Show loading until performance settings are determined
  if (!performanceSettings) {
    return <CanvasLoader />;
  }

  // For very low performance devices, show a simple fallback
  if (performanceSettings.isLowPerformance && performanceSettings.isMobile) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-900/20 to-blue-900/20 rounded-lg">
        <div className="text-center">
          <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg animate-pulse"></div>
          <p className="text-white/70 text-sm">3D Computer Model</p>
        </div>
      </div>
    );
  }

  return (
    <Canvas
      frameloop='demand'
      shadows={performanceSettings.enableShadows}
      dpr={performanceSettings.pixelRatio}
      camera={{ position: [20, 3, 5], fov: 25 }}
      gl={{
        preserveDrawingBuffer: true,
        antialias: performanceSettings.antialias,
        powerPreference: "high-performance"
      }}
    >
      <Suspense fallback={<CanvasLoader />}>
        <OrbitControls
          enableZoom={false}
          maxPolarAngle={Math.PI / 2}
          minPolarAngle={Math.PI / 2}
        />
        <Computers isMobile={isMobile} performanceSettings={performanceSettings} />
      </Suspense>

      <Preload all />
    </Canvas>
  );
};

export default ComputersCanvas;
