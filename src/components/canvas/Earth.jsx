import React, { Suspense, useState, useEffect } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Preload, useGLTF } from "@react-three/drei";

import CanvasLoader from "../Loader";
import { getPerformanceSettings } from "../../utils/deviceDetection";

const Earth = () => {
  const earth = useGLTF("./planet/scene.gltf");

  return (
    <primitive object={earth.scene} scale={2.5} position-y={0} rotation-y={0} />
  );
};

const EarthCanvas = () => {
  const [performanceSettings, setPerformanceSettings] = useState(null);

  useEffect(() => {
    setPerformanceSettings(getPerformanceSettings());
  }, []);

  // Show loading until performance settings are determined
  if (!performanceSettings) {
    return <CanvasLoader />;
  }

  // For very low performance devices, show a simple fallback
  if (performanceSettings.isLowPerformance && performanceSettings.isMobile) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-900/20 to-blue-900/20 rounded-lg">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-green-500 to-blue-500 rounded-full animate-spin"></div>
          <p className="text-white/70 text-sm">Earth Model</p>
        </div>
      </div>
    );
  }

  return (
    <Canvas
      shadows={performanceSettings.enableShadows}
      frameloop='demand'
      dpr={performanceSettings.pixelRatio}
      gl={{
        preserveDrawingBuffer: true,
        antialias: performanceSettings.antialias,
        powerPreference: "high-performance"
      }}
      camera={{
        fov: 45,
        near: 0.1,
        far: 200,
        position: [-4, 3, 6],
      }}
    >
      <Suspense fallback={<CanvasLoader />}>
        <OrbitControls
          autoRotate
          enableZoom={false}
          maxPolarAngle={Math.PI / 2}
          minPolarAngle={Math.PI / 2}
        />
        <Earth />

        <Preload all />
      </Suspense>
    </Canvas>
  );
};

export default EarthCanvas;
