"use client";

import React, { useEffect, memo } from "react";
import { Sparkles, FileText, Code } from "lucide-react";
import { motion } from "framer-motion";
import Tilt from "react-parallax-tilt";
import AOS from "aos";
import "aos/dist/aos.css";
import { services } from "../constants";
import { SectionWrapper } from "../hoc";
import { fadeIn } from "../utils/motion";

// Memoized Header
const Header = memo(() => (
  <div className="text-center lg:mb-8 mb-2">
    <h2
      className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#6366f1] to-[#a855f7]"
      data-aos="zoom-in-up"
      data-aos-duration="600"
    >
      About Me
    </h2>
    <p
      className="mt-2 text-gray-400 max-w-2xl mx-auto text-base sm:text-lg flex items-center justify-center gap-2"
      data-aos="zoom-in-up"
      data-aos-duration="800"
    >
      <Sparkles className="w-5 h-5 text-purple-400" />
      Transforming ideas into digital experiences
      <Sparkles className="w-5 h-5 text-purple-400" />
    </p>
  </div>
));

// Profile Image Component with Animated Circle
const ProfileImage = memo(() => (
  <div className="relative flex items-center justify-center">
    {/* Animated circle */}
    <motion.svg
      className="absolute w-[260px] xl:w-[460px] h-[260px] xl:h-[460px]"
      fill="transparent"
      viewBox='0 0 460 460'
      xmlns='http://www.w3.org/2000/svg'
    >
      <motion.circle
        cx='230'
        cy='230'
        r='230'
        stroke='#6366f1'
        strokeWidth='8'
        strokeLinecap='round'
        strokeLinejoin='round'
        initial={{strokeDasharray:'24 10 0 0'}}
        animate={{strokeDasharray:['15 120 25 25 ','16 25 92 72' ,'4 250 22 22']}}
        transition={{duration:1.4, repeat:Infinity, repeatType:'reverse'}}
      />
    </motion.svg>

    {/* Profile Image */}
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1, delay: 0.4 }}
      className="w-[250px] h-[250px] xl:w-[450px] xl:h-[450px] mix-blend-lighten relative"
    >
      <img
        src="/profileimg.png"
        alt="Sreeraj profile"
        className="w-full h-full object-contain"
      />
    </motion.div>
  </div>
));

// Service Card Component - Exact design match
const ServiceCard = ({ index, title, icon }) => (
  <Tilt
    tiltMaxAngleX={45}
    tiltMaxAngleY={45}
    scale={1}
    transitionSpeed={450}
    className='xs:w-[250px] w-full'
  >
    <motion.div
      variants={fadeIn("right", "spring", index * 0.5, 0.75)}
      className='w-full green-pink-gradient p-[1px] rounded-[20px] shadow-card'
    >
      <div
        className='bg-tertiary rounded-[20px] py-5 px-12 min-h-[280px] flex justify-evenly items-center flex-col'
      >
        <img
          src={icon}
          alt='web-development'
          className='w-16 h-16 object-contain'
        />

        <h3 className='text-white text-[20px] font-bold text-center'>
          {title}
        </h3>
      </div>
    </motion.div>
  </Tilt>
);

const About = () => {
  // Initialize AOS animations
  useEffect(() => {
    AOS.init({ once: false });

    // Handle window resize for reinitializing AOS
    let resizeTimer;
    const handleResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(AOS.refresh, 250);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
      clearTimeout(resizeTimer);
    };
  }, []);

  // Services Data for new card design
  const servicesData = services;

  return (
    <div
      className="min-h-screen w-full text-white overflow-hidden"
      id="about"
    >
      {/* Removed unnecessary container wrapper - direct full-width layout */}
      <div className="w-full min-h-screen flex flex-col justify-center px-4 sm:px-6 lg:px-8">
        <Header />

        <div className="w-full pt-8 sm:pt-12 relative flex-1 flex flex-col justify-center">
          {/* Removed constraining grid container - direct flex layout */}
          <div className="flex flex-col-reverse lg:flex-row gap-10 lg:gap-16 items-center mb-16">
            {/* Removed nested spacing div - direct content positioning */}
            <div className="w-full lg:w-1/2 space-y-6 text-center lg:text-left">
              <h2
                className="text-3xl sm:text-4xl lg:text-5xl font-bold"
                data-aos="fade-right"
                data-aos-duration="1000"
              >
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#6366f1] to-[#a855f7]">
                  Hello, I'm
                </span>
                <span
                  className="block mt-2 text-gray-200"
                  data-aos="fade-right"
                  data-aos-duration="1300"
                >
                  Sreeraj
                </span>
              </h2>

              <p
                className="text-base sm:text-lg lg:text-xl text-gray-400 leading-relaxed text-justify pb-4 sm:pb-0"
                data-aos="fade-right"
                data-aos-duration="1500"
              >
                I'm a skilled software developer with experience in TypeScript and
                JavaScript, and expertise in frameworks like React, Node.js, and
                Three.js. I'm a quick learner and collaborate closely with clients to
                create efficient, scalable, and user-friendly solutions that solve
                real-world problems. Let's work together to bring your ideas to life!
              </p>

              <div className="flex flex-col lg:flex-row items-center lg:items-start gap-4 lg:gap-4 lg:px-0 w-full">
                <a href="/SreerajResume.pdf" download="SreerajResume.pdf" target="_blank" rel="noopener noreferrer" className="w-full lg:w-auto">
                <button
                  data-aos="fade-up"
                  data-aos-duration="800"
                  className="w-full lg:w-auto sm:px-6 py-2 sm:py-3 rounded-lg bg-gradient-to-r from-[#6366f1] to-[#a855f7] text-white font-medium transition-all duration-300 hover:scale-105 flex items-center justify-center lg:justify-start gap-2 shadow-lg hover:shadow-xl animate-bounce-slow"
                >
                  <FileText className="w-4 h-4 sm:w-5 sm:h-5" /> Download CV
                </button>
                </a>
                <a href="#work" className="w-full lg:w-auto">
                <button
                  data-aos="fade-up"
                  data-aos-duration="1000"
                  className="w-full lg:w-auto sm:px-6 py-2 sm:py-3 rounded-lg border border-[#a855f7]/50 text-[#a855f7] font-medium transition-all duration-300 hover:scale-105 flex items-center justify-center lg:justify-start gap-2 hover:bg-[#a855f7]/10 animate-bounce-slow delay-200"
                >
                  <Code className="w-4 h-4 sm:w-5 sm:h-5" /> View Projects
                </button>
                </a>
              </div>
            </div>

            {/* Removed constraining wrapper div */}
            <div className="w-full lg:w-1/2 flex justify-center">
              <ProfileImage />
            </div>
          </div>

          {/* Removed max-width constraint - full viewport width */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
            {servicesData.map((service, index) => (
              <ServiceCard key={service.title} index={index} title={service.title} icon={service.icon} />
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-20px); }
        }
        @keyframes spin-slower {
          to { transform: rotate(360deg); }
        }
        .animate-bounce-slow {
          animation: bounce 3s infinite;
        }
        .animate-pulse-slow {
          animation: pulse 3s infinite;
        }
        .animate-spin-slower {
          animation: spin-slower 8s linear infinite;
        }
      `}</style>
    </div>
  );
};

export default SectionWrapper(About, "about");
